import braintrust
from pydantic import BaseModel
from typing import Optional, List, Dict, Any


# Create project for the support tools
project = braintrust.projects.create(name="pedro-project1")


# Tool 1: ask_clarifying_question
class ClarifyingQuestionInput(BaseModel):
    question: str
    context: Optional[str] = None


def ask_clarifying_question(question: str, context: Optional[str] = None) -> Dict[str, Any]:
    """
    Dummy implementation of ask_clarifying_question tool.
    In a real implementation, this would present a question to the customer.
    """
    print(f"Asking clarifying question: {question}")
    if context:
        print(f"Context: {context}")
    
    return {
        "status": "question_asked",
        "question": question,
        "context": context,
        "response": "This is a dummy response to the clarifying question."
    }


ask_clarifying_question_tool = project.tools.create(
    handler=ask_clarifying_question,
    name="Ask Clarifying Question",
    slug="ask-clarifying-question",
    description="Ask a clarifying question to better understand the customer's issue or request",
    parameters=ClarifyingQuestionInput,
    if_exists="replace",
)


# Tool 2: send_external_message
class ExternalMessageInput(BaseModel):
    message: str
    recipient: Optional[str] = None
    format: Optional[str] = "html"  # html or text


def send_external_message(message: str, recipient: Optional[str] = None, format: Optional[str] = "html") -> Dict[str, Any]:
    """
    Dummy implementation of send_external_message tool.
    In a real implementation, this would send a message to the customer.
    """
    print(f"Sending external message (format: {format}):")
    print(f"To: {recipient or 'customer'}")
    print(f"Message: {message}")
    
    return {
        "status": "message_sent",
        "message": message,
        "recipient": recipient or "customer",
        "format": format,
        "timestamp": "2025-09-02T12:00:00Z"
    }


send_external_message_tool = project.tools.create(
    handler=send_external_message,
    name="Send External Message",
    slug="send-external-message",
    description="Send a message to the customer or external recipient",
    parameters=ExternalMessageInput,
    if_exists="replace",
)


# Tool 3: query_knowledge_base
class KnowledgeBaseQueryInput(BaseModel):
    query: str
    max_results: Optional[int] = 5
    category: Optional[str] = None


def query_knowledge_base(query: str, max_results: Optional[int] = 5, category: Optional[str] = None) -> Dict[str, Any]:
    """
    Dummy implementation of query_knowledge_base tool.
    In a real implementation, this would search the knowledge base for relevant information.
    """
    print(f"Querying knowledge base: {query}")
    if category:
        print(f"Category filter: {category}")
    
    # Return dummy knowledge base results
    dummy_results = [
        {
            "id": "kb_001",
            "title": "API Authentication Guide",
            "content": "To authenticate with the Together AI API, you need to include your API key in the Authorization header...",
            "url": "https://docs.together.ai/authentication",
            "relevance_score": 0.95,
            "category": "authentication"
        },
        {
            "id": "kb_002", 
            "title": "Rate Limits and Quotas",
            "content": "Together AI implements rate limiting to ensure fair usage across all customers...",
            "url": "https://docs.together.ai/rate-limits",
            "relevance_score": 0.87,
            "category": "limits"
        },
        {
            "id": "kb_003",
            "title": "Model Selection Guide", 
            "content": "Choose the right model for your use case. We offer various models optimized for different tasks...",
            "url": "https://docs.together.ai/models",
            "relevance_score": 0.82,
            "category": "models"
        }
    ]
    
    # Filter by category if specified
    if category:
        dummy_results = [r for r in dummy_results if r["category"] == category]
    
    # Limit results
    dummy_results = dummy_results[:max_results]
    
    return {
        "status": "search_completed",
        "query": query,
        "results": dummy_results,
        "total_found": len(dummy_results)
    }


query_knowledge_base_tool = project.tools.create(
    handler=query_knowledge_base,
    name="Query Knowledge Base",
    slug="query-knowledge-base", 
    description="Search the knowledge base for information relevant to customer inquiries",
    parameters=KnowledgeBaseQueryInput,
    if_exists="replace",
)


# Tool 4: waiting_on_customer
class WaitingOnCustomerInput(BaseModel):
    reason: str
    ticket_id: Optional[str] = None
    follow_up_date: Optional[str] = None


def waiting_on_customer(reason: str, ticket_id: Optional[str] = None, follow_up_date: Optional[str] = None) -> Dict[str, Any]:
    """
    Dummy implementation of waiting_on_customer tool.
    In a real implementation, this would mark a ticket as waiting for customer response.
    """
    print(f"Marking ticket as waiting on customer")
    print(f"Reason: {reason}")
    if ticket_id:
        print(f"Ticket ID: {ticket_id}")
    if follow_up_date:
        print(f"Follow-up date: {follow_up_date}")
    
    return {
        "status": "waiting_on_customer",
        "reason": reason,
        "ticket_id": ticket_id or "DUMMY_TICKET_001",
        "follow_up_date": follow_up_date,
        "timestamp": "2025-09-02T12:00:00Z"
    }


waiting_on_customer_tool = project.tools.create(
    handler=waiting_on_customer,
    name="Waiting on Customer",
    slug="waiting-on-customer",
    description="Mark a support ticket as waiting for customer response",
    parameters=WaitingOnCustomerInput,
    if_exists="replace",
)


# Print tool information for verification
print("Created dummy support tools:")
print(f"1. {ask_clarifying_question_tool.name} (slug: {ask_clarifying_question_tool.slug})")
print(f"2. {send_external_message_tool.name} (slug: {send_external_message_tool.slug})")
print(f"3. {query_knowledge_base_tool.name} (slug: {query_knowledge_base_tool.slug})")
print(f"4. {waiting_on_customer_tool.name} (slug: {waiting_on_customer_tool.slug})")
